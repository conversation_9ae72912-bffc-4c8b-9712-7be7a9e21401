# MCP Financial Analyzer WebSocket Interface

A FastAPI WebSocket implementation for the MCP Financial Analyzer that provides real-time financial analysis through specialized AI agents.

## Overview

This WebSocket interface exposes four specialized endpoints for different aspects of financial analysis:

- **🔍 Research** (`/ws/research/{user_id}`) - Gather comprehensive financial data using AI-powered research agents
- **📊 Analyze** (`/ws/analyze/{user_id}`) - Perform detailed financial analysis on provided data
- **📄 Report** (`/ws/report/{user_id}`) - Generate professional financial reports
- **🎯 Full Analysis** (`/ws/full_analysis/{user_id}`) - Complete end-to-end analysis workflow

## Features

- **Real-time Streaming**: Get live updates as agents work on your requests
- **Session Management**: Persistent sessions with conversation history
- **Specialized Agents**: Each endpoint uses purpose-built financial analysis agents
- **Professional Reports**: Automatically generated and saved financial reports
- **Web Interface**: Built-in HTML interface for easy testing and interaction
- **Multiple Companies**: Analyze different companies simultaneously with separate sessions

## Quick Start

### 1. Install Dependencies

```bash
cd /merge/mcp-agent/examples/usecases/mcp_financial_analyzer/fastapi_websocket/
pip install -r requirements.txt
```

### 2. Configure MCP Servers

Ensure you have the required MCP servers installed:

```bash
# Google Search MCP server
npm install -g g-search-mcp

# Filesystem server
npm install -g @modelcontextprotocol/server-filesystem

# Fetch server (usually comes with mcp-agent)
uvx mcp-server-fetch
```

### 3. Start the Server

```bash
python main.py
```

The server will start on `http://localhost:8000`

### 4. Access the Web Interface

Open your browser and navigate to `http://localhost:8000` to access the interactive web interface.

## API Endpoints

### WebSocket Endpoints

#### Research Endpoint
```
ws://localhost:8000/ws/research/{user_id}?company={company_name}
```
Specialized for gathering financial data using Google Search and web scraping.

#### Analyze Endpoint
```
ws://localhost:8000/ws/analyze/{user_id}?company={company_name}
```
Focused on analyzing financial data and providing insights.

#### Report Endpoint
```
ws://localhost:8000/ws/report/{user_id}?company={company_name}
```
Generates professional financial reports and saves them to the `company_reports/` directory.

#### Full Analysis Endpoint
```
ws://localhost:8000/ws/full_analysis/{user_id}?company={company_name}
```
Orchestrates the complete workflow: research → analysis → report generation.

### REST Endpoints

#### Health Check
```
GET /health
```
Returns server status and active session count.

#### List Sessions
```
GET /sessions
```
Returns information about all active sessions.

#### Get Session Info
```
GET /sessions/{user_id}/{session_type}
```
Returns detailed information about a specific session.

## Usage Examples

### Using the Web Interface

1. Open `http://localhost:8000` in your browser
2. Choose an endpoint (Research, Analyze, Report, or Full Analysis)
3. Enter a User ID and Company Name
4. Click "Connect" to establish a WebSocket connection
5. Type your request in the input field and click "Send"
6. Watch real-time responses stream in

### Using the Python Client

The included `financial_websocket_client.py` provides a command-line interface:

```bash
# Interactive session with research endpoint
python financial_websocket_client.py --endpoint research --company "Tesla Inc." --interactive

# Test all endpoints
python financial_websocket_client.py --test-all --company "Microsoft"

# Quick test of analyze endpoint
python financial_websocket_client.py --endpoint analyze --company "Google"
```

### Using WebSocket Libraries

Example with Python `websockets`:

```python
import asyncio
import json
import websockets

async def test_research():
    uri = "ws://localhost:8000/ws/research/my_user?company=Apple Inc."
    
    async with websockets.connect(uri) as websocket:
        # Send research request
        await websocket.send(json.dumps({
            "message": "Please research Apple's latest quarterly earnings"
        }))
        
        # Listen for responses
        async for message in websocket:
            data = json.loads(message)
            print(f"{data['type']}: {data['message']}")

asyncio.run(test_research())
```

## Message Format

### Incoming Messages (Client → Server)
```json
{
    "message": "Your request or question here"
}
```

### Outgoing Messages (Server → Client)
```json
{
    "type": "system|progress|result|error",
    "message": "Response content",
    "user_id": "user_identifier",
    "session_id": "session_uuid",
    "company_name": "Company Name",
    "timestamp": "ISO timestamp"
}
```

## Configuration

### MCP Configuration (`mcp_agent.config.yaml`)

The configuration includes:
- **Execution Engine**: AsyncIO for concurrent processing
- **Logging**: Debug level with file output to `logs/` directory
- **MCP Servers**: Google Search, Filesystem, and Fetch servers
- **LLM Providers**: VLLM (primary) and OpenAI (fallback)

### Secrets Configuration (`mcp_agent.secrets.yaml`)

Configure your API keys:
- OpenAI API key for fallback LLM
- VLLM typically doesn't require an API key for local deployment

## Architecture

### Session Management
- Each endpoint type creates specialized sessions
- Sessions maintain conversation history and agent state
- Automatic cleanup of inactive sessions (configurable)
- Support for multiple concurrent sessions per user

### Agent Specialization
- **Research Agent**: Uses Google Search and web fetch capabilities
- **Analyst Agent**: Focuses on financial analysis and insights
- **Report Writer**: Generates professional reports with filesystem access
- **Orchestrator**: Coordinates all agents for full analysis workflow

### Error Handling
- Graceful WebSocket disconnection handling
- Agent initialization error recovery
- Session cleanup on failures
- Detailed error messages for debugging

## Output

### Generated Reports
Reports are automatically saved to the `company_reports/` directory with timestamped filenames:
```
company_reports/
├── apple_inc._report_20250718_143022.md
├── tesla_report_20250718_144155.md
└── microsoft_report_20250718_145301.md
```

### Logs
Detailed logs are saved to the `logs/` directory in JSONL format for analysis and debugging.

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure the server is running on port 8000
2. **MCP Server Errors**: Verify all required MCP servers are installed
3. **API Key Issues**: Check your OpenAI API key in `mcp_agent.secrets.yaml`
4. **Permission Errors**: Ensure write permissions for `company_reports/` and `logs/` directories

### Debug Mode

Enable debug logging by setting the log level in `mcp_agent.config.yaml`:
```yaml
logger:
  level: debug
```

## Development

### Adding New Endpoints

1. Define a new `SessionType` in `session_manager.py`
2. Add agent initialization logic in `_initialize_agents()`
3. Create a new WebSocket endpoint in `main.py`
4. Update the HTML interface to include the new endpoint

### Customizing Agents

Modify the agent factory functions in the parent directory:
- `../agents/research_agent.py`
- `../agents/analyst_agent.py`
- `../agents/report_writer.py`

## License

This project is part of the MCP Agent framework. See the main project license for details.
