#!/usr/bin/env python3
"""
Integration test for WebSocket MCP streaming functionality.

This test starts the actual FastAPI server and tests real WebSocket connections
with MCP service streaming.
"""

import asyncio
import json
import pytest
import websockets
import subprocess
import time
import signal
import os
import requests
from typing import List, Dict, Any


class WebSocketMCPStreamingTest:
    """Integration test for WebSocket MCP streaming."""
    
    def __init__(self):
        self.server_process = None
        self.server_url = "ws://localhost:8000"
        
    async def check_server_health(self):
        """Check if the server is running."""
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    async def start_server(self):
        """Check if server is already running, skip starting if it is."""
        if await self.check_server_health():
            print("✅ Server is already running")
            return

        print("❌ Server not running - please start the server manually with: python main.py")
        raise Exception("Server not available")

    async def stop_server(self):
        """Skip stopping server since we didn't start it."""
        pass
    
    async def test_websocket_mcp_streaming(self):
        """Test WebSocket connection with MCP streaming."""
        endpoint_url = f"{self.server_url}/ws/research/test_user?company=Apple Inc."
        
        try:
            async with websockets.connect(endpoint_url) as websocket:
                # Send research request
                request = {
                    "message": "Research Apple Inc. current stock price",
                    "streaming": True
                }
                await websocket.send(json.dumps(request))
                
                # Collect messages
                messages = []
                timeout_count = 0
                max_timeout = 30  # 30 second timeout
                
                while timeout_count < max_timeout:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        msg_data = json.loads(message)
                        messages.append(msg_data)
                        
                        print(f"Received: {msg_data.get('type')} - {msg_data.get('message', '')[:100]}")
                        
                        # Check for stream end
                        if msg_data.get("type") == "stream_end":
                            break
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        continue
                
                return messages
                
        except Exception as e:
            print(f"WebSocket connection failed: {e}")
            return []
    
    async def run_integration_test(self):
        """Run the complete integration test."""
        print("🚀 Starting WebSocket MCP Streaming Integration Test")
        
        try:
            # Start server
            print("📡 Starting FastAPI server...")
            await self.start_server()
            print("✅ Server started successfully")
            
            # Test WebSocket streaming
            print("🔗 Testing WebSocket connection...")
            messages = await self.test_websocket_mcp_streaming()
            
            # Analyze results
            print(f"\n📊 Test Results:")
            print(f"   Messages received: {len(messages)}")
            
            if messages:
                message_types = [msg.get("type") for msg in messages]
                print(f"   Message types: {set(message_types)}")
                
                # Check for expected message types
                has_stream_chunks = "stream_chunk" in message_types
                has_mcp_streaming = "mcp_tool_stream" in message_types
                has_stream_end = "stream_end" in message_types
                
                print(f"   ✅ LLM streaming: {has_stream_chunks}")
                print(f"   🔧 MCP tool streaming: {has_mcp_streaming}")
                print(f"   🏁 Stream completion: {has_stream_end}")
                
                if has_stream_chunks and has_stream_end:
                    print("\n🎉 SUCCESS: Basic streaming is working!")
                    if has_mcp_streaming:
                        print("🎉 SUCCESS: MCP tool streaming is working!")
                    else:
                        print("⚠️  MCP tool streaming not detected (may need implementation)")
                else:
                    print("\n❌ FAILED: Basic streaming not working properly")
            else:
                print("❌ FAILED: No messages received")
                
        except Exception as e:
            print(f"❌ FAILED: Integration test error: {e}")
            
        finally:
            # Stop server
            print("🛑 Stopping server...")
            await self.stop_server()
            print("✅ Server stopped")


@pytest.mark.asyncio
async def test_websocket_mcp_streaming_integration():
    """Pytest wrapper for the integration test."""
    test = WebSocketMCPStreamingTest()
    await test.run_integration_test()


async def main():
    """Run the integration test directly."""
    test = WebSocketMCPStreamingTest()
    await test.run_integration_test()


if __name__ == "__main__":
    asyncio.run(main())
