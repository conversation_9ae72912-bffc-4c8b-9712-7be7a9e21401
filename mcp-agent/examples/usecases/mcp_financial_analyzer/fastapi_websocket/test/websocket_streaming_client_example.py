#!/usr/bin/env python3
"""
Example WebSocket client that demonstrates proper handling of streaming responses
with both text content and tool call events.
"""

import asyncio
from urllib.parse import quote
import json
import websockets
import argparse
from datetime import datetime
from typing import Dict, Any


class StreamingWebSocketClient:
    """Example client for WebSocket streaming with tool call support."""

    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.user_id = "demo_user"
        self.company = "Apple Inc."

    async def connect_basic_websocket(self):
        """Connect to basic WebSocket endpoint with streaming support."""
        uri = f"{self.base_url}/ws/{self.user_id}"
        print(f"🔗 Connecting to basic WebSocket: {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                print("✅ Connected to basic WebSocket")
                await self.interactive_session(websocket, "Basic WebSocket")
        except Exception as e:
            print(f"❌ Failed to connect to basic WebSocket: {e}")

    async def connect_financial_websocket(self, endpoint: str = "research"):
        """Connect to financial analyzer WebSocket endpoint."""
        uri = f"{self.base_url}/ws/{endpoint}/{self.user_id}?company={quote(self.company)}"
        print(f"🔗 Connecting to financial WebSocket: {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                print(f"✅ Connected to financial WebSocket ({endpoint})")
                await self.interactive_session(websocket, f"Financial {endpoint.title()}")
        except Exception as e:
            print(f"❌ Failed to connect to financial WebSocket: {e}")

    async def interactive_session(self, websocket, session_name: str):
        """Run an interactive session with the WebSocket."""
        print(f"\n🎯 Starting {session_name} Session")
        print("=" * 50)
        print("Commands:")
        print("  - Type your message and press Enter")
        print("  - Type 'quit' to exit")
        print("  - Type 'streaming:off' to disable streaming")
        print("  - Type 'streaming:on' to enable streaming")
        print("=" * 50)

        streaming_enabled = True

        while True:
            try:
                # Get user input
                user_input = input(f"\n[{session_name}] You: ").strip()
                
                if user_input.lower() == 'quit':
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'streaming:off':
                    streaming_enabled = False
                    print("📴 Streaming disabled")
                    continue
                elif user_input.lower() == 'streaming:on':
                    streaming_enabled = True
                    print("📡 Streaming enabled")
                    continue
                elif not user_input:
                    continue

                # Send message
                message = {
                    "message": user_input,
                    "streaming": streaming_enabled
                }
                
                print(f"📤 Sending: {user_input}")
                await websocket.send(json.dumps(message))

                # Handle response
                if streaming_enabled:
                    await self.handle_streaming_response(websocket, session_name)
                else:
                    await self.handle_non_streaming_response(websocket, session_name)

            except KeyboardInterrupt:
                print("\n👋 Session interrupted by user")
                break
            except Exception as e:
                print(f"❌ Error in session: {e}")
                break

    async def handle_streaming_response(self, websocket, session_name: str):
        """Handle streaming response with real-time updates."""
        print(f"\n[{session_name}] Assistant: ", end="", flush=True)
        
        full_response = ""
        tool_calls = []
        
        while True:
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=120.0)
                data = json.loads(response)
                
                if data["type"] == "stream_start":
                    print(f"\n🟢 {data.get('message', 'Stream started')}")
                    print(f"[{session_name}] Assistant: ", end="", flush=True)
                    
                elif data["type"] == "stream_chunk":
                    chunk = data["message"]
                    print(chunk, end="", flush=True)
                    full_response += chunk
                    
                elif data["type"] == "tool_stream":
                    # Handle tool call events
                    tool_event = data["data"]
                    await self.handle_tool_event(tool_event, session_name)
                    tool_calls.append(tool_event)
                    
                elif data["type"] == "mcp_tool_stream":
                    # Handle MCP tool call events (financial analyzer)
                    tool_event = data["data"]
                    await self.handle_tool_event(tool_event, session_name)
                    tool_calls.append(tool_event)
                    
                elif data["type"] == "stream_end":
                    print(f"\n🔴 {data.get('message', 'Stream ended')}")
                    if tool_calls:
                        print(f"🔧 Total tool calls: {len(tool_calls)}")
                    break
                    
                elif data["type"] == "error":
                    print(f"\n❌ Error: {data['message']}")
                    break
                    
            except asyncio.TimeoutError:
                print("\n⏰ Timeout waiting for response")
                break
            except json.JSONDecodeError as e:
                print(f"\n❌ Invalid JSON response: {e}")
                break

    async def handle_non_streaming_response(self, websocket, session_name: str):
        """Handle non-streaming response."""
        try:
            response = await asyncio.wait_for(websocket.recv(), timeout=120.0)
            data = json.loads(response)
            
            if data["type"] == "result":
                print(f"\n[{session_name}] Assistant: {data['message']}")
            elif data["type"] == "error":
                print(f"\n❌ Error: {data['message']}")
            else:
                print(f"\n📨 Received: {data}")
                
        except asyncio.TimeoutError:
            print("\n⏰ Timeout waiting for response")
        except json.JSONDecodeError as e:
            print(f"\n❌ Invalid JSON response: {e}")

    async def handle_tool_event(self, tool_event: Dict[str, Any], session_name: str):
        """Handle tool call events with real-time display."""
        event_type = tool_event.get("type", "unknown")
        tool_name = tool_event.get("tool_name", "unknown")
        timestamp = tool_event.get("timestamp", "")
        
        if event_type == "tool_call_start":
            args = tool_event.get("args", {})
            print(f"\n🔧 [{timestamp}] Calling tool: {tool_name}")
            if args:
                print(f"   📋 Args: {json.dumps(args, indent=2)}")
            print(f"[{session_name}] Assistant: ", end="", flush=True)
            
        elif event_type == "tool_call_result":
            execution_time = tool_event.get("execution_time", 0)
            result = tool_event.get("result", {})
            print(f"\n✅ [{timestamp}] Tool {tool_name} completed in {execution_time:.2f}s")
            
            # Display result summary
            if isinstance(result, dict) and "content" in result:
                content = result["content"]
                if isinstance(content, list) and content:
                    # Show first 100 chars of first content item
                    first_content = str(content[0])[:100]
                    print(f"   📄 Result: {first_content}{'...' if len(str(content[0])) > 100 else ''}")
            
            print(f"[{session_name}] Assistant: ", end="", flush=True)
            
        elif event_type == "tool_call_error":
            error = tool_event.get("error", "Unknown error")
            execution_time = tool_event.get("execution_time", 0)
            print(f"\n❌ [{timestamp}] Tool {tool_name} failed after {execution_time:.2f}s")
            print(f"   🚨 Error: {error}")
            print(f"[{session_name}] Assistant: ", end="", flush=True)

    async def demo_session(self):
        """Run a demonstration session with predefined messages."""
        print("🎬 Running Demo Session")
        print("=" * 50)
        
        # Demo with basic WebSocket
        print("\n📋 Demo 1: Basic WebSocket with File Operations")
        uri = f"{self.base_url}/ws/{self.user_id}"
        
        try:
            async with websockets.connect(uri) as websocket:
                demo_messages = [
                    "List the files in the current directory",
                    "What is the current date and time?",
                    "Create a simple text file with hello world"
                ]
                
                for msg in demo_messages:
                    print(f"\n📤 Demo message: {msg}")
                    message = {"message": msg, "streaming": True}
                    await websocket.send(json.dumps(message))
                    await self.handle_streaming_response(websocket, "Demo Basic")
                    await asyncio.sleep(2)  # Pause between messages
                    
        except Exception as e:
            print(f"❌ Demo basic WebSocket failed: {e}")

        # Demo with financial WebSocket
        print(f"\n📋 Demo 2: Financial WebSocket - Research {self.company}")
        uri = f"{self.base_url}/ws/research/{self.user_id}?company={quote(self.company)}"
        
        try:
            async with websockets.connect(uri) as websocket:
                demo_message = f"Research the current stock price and recent news for {self.company}"
                print(f"\n📤 Demo message: {demo_message}")
                message = {"message": demo_message, "streaming": True}
                await websocket.send(json.dumps(message))
                await self.handle_streaming_response(websocket, "Demo Financial")
                    
        except Exception as e:
            print(f"❌ Demo financial WebSocket failed: {e}")


async def main():
    """Main function to run the WebSocket client."""
    parser = argparse.ArgumentParser(description="WebSocket Streaming Client Example")
    parser.add_argument("--url", default="ws://localhost:8000", help="WebSocket server URL")
    parser.add_argument("--mode", choices=["interactive", "demo", "basic", "financial"], 
                       default="interactive", help="Client mode")
    parser.add_argument("--endpoint", choices=["research", "analyze", "report", "full_analysis"], 
                       default="research", help="Financial endpoint to use")
    parser.add_argument("--company", default="Apple Inc.", help="Company for financial analysis")
    
    args = parser.parse_args()
    
    client = StreamingWebSocketClient(args.url)
    client.company = args.company
    
    print("🚀 WebSocket Streaming Client")
    print(f"🔗 Server: {args.url}")
    print(f"👤 User ID: {client.user_id}")
    print(f"🏢 Company: {client.company}")
    
    try:
        if args.mode == "demo":
            await client.demo_session()
        elif args.mode == "basic":
            await client.connect_basic_websocket()
        elif args.mode == "financial":
            await client.connect_financial_websocket(args.endpoint)
        else:  # interactive
            print("\nSelect connection type:")
            print("1. Basic WebSocket")
            print("2. Financial Research")
            print("3. Financial Analysis")
            print("4. Financial Report")
            print("5. Full Financial Analysis")
            
            choice = input("Enter choice (1-5): ").strip()
            
            if choice == "1":
                await client.connect_basic_websocket()
            elif choice == "2":
                await client.connect_financial_websocket("research")
            elif choice == "3":
                await client.connect_financial_websocket("analyze")
            elif choice == "4":
                await client.connect_financial_websocket("report")
            elif choice == "5":
                await client.connect_financial_websocket("full_analysis")
            else:
                print("❌ Invalid choice")
                
    except KeyboardInterrupt:
        print("\n👋 Client interrupted by user")
    except Exception as e:
        print(f"❌ Client error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
