#!/usr/bin/env python3
"""
Debug session initialization issues.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from session_manager import FinancialSession, SessionType


async def test_session_initialization():
    """Test session initialization to debug issues."""
    
    print("🔧 Testing session initialization...")
    
    try:
        # Create a research session
        session = FinancialSession("debug_user", "debug_session", SessionType.RESEARCH)
        print("✅ Session object created")
        
        # Try to initialize
        print("🔄 Initializing session...")
        await session.initialize("Apple Inc.")
        print("✅ Session initialized successfully")
        
        # Check components
        print(f"📊 Session components:")
        print(f"   Research agent: {session.research_agent is not None}")
        print(f"   LLM: {session.llm is not None}")
        print(f"   Streaming LLM: {session.streaming_llm is not None}")
        print(f"   MCP streaming wrapper: {session.mcp_streaming_wrapper is not None}")
        
        # Try a simple message processing
        print("💬 Testing message processing...")
        response = await session.process_message("Hello, test message")
        print(f"✅ Message processed: {len(response)} characters")
        
        # Cleanup
        await session.cleanup()
        print("✅ Session cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Session initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the session debug test."""
    print("🚀 Session Initialization Debug Test")
    print("=" * 50)
    
    success = await test_session_initialization()
    
    if success:
        print("\n🎉 Session initialization working!")
    else:
        print("\n❌ Session initialization failed!")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
