#!/usr/bin/env python3
"""
Comprehensive test to validate MCP streaming functionality with the running server.
"""

import asyncio
import json
import websockets
import requests
from datetime import datetime


class MCPStreamingValidator:
    """Validates MCP streaming functionality with real server."""
    
    def __init__(self):
        self.server_url = "ws://localhost:8000"
        self.results = {
            "test_timestamp": datetime.now().isoformat(),
            "tests_run": 0,
            "tests_passed": 0,
            "streaming_messages": [],
            "mcp_tool_messages": [],
            "errors": []
        }
    
    async def check_server_health(self):
        """Check if server is running."""
        try:
            response = requests.get("http://localhost:8000/health")
            return response.status_code == 200
        except:
            return False
    
    async def test_research_streaming(self):
        """Test research endpoint with streaming."""
        print("🔬 Testing research streaming...")
        
        endpoint_url = f"{self.server_url}/ws/research/test_user?company=Apple Inc."
        
        try:
            async with websockets.connect(endpoint_url) as websocket:
                # Send research request
                request = {
                    "message": "Research Apple Inc. current stock price and recent news",
                    "streaming": True
                }
                
                print(f"📤 Sending request: {request['message']}")
                await websocket.send(json.dumps(request))
                
                # Collect streaming messages
                messages = []
                timeout_count = 0
                max_timeout = 45  # 45 second timeout for research
                
                while timeout_count < max_timeout:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        msg_data = json.loads(message)
                        messages.append(msg_data)
                        
                        msg_type = msg_data.get("type", "unknown")
                        
                        if msg_type == "stream_chunk":
                            content = msg_data.get("message", "")[:100]
                            print(f"📦 Stream chunk: {content}...")
                        elif msg_type == "mcp_tool_stream":
                            tool_data = msg_data.get("data", {})
                            tool_type = tool_data.get("type", "unknown")
                            tool_name = tool_data.get("tool_name", "unknown")
                            print(f"🔧 MCP Tool {tool_type}: {tool_name}")
                            self.results["mcp_tool_messages"].append(msg_data)
                        elif msg_type == "stream_end":
                            print(f"🏁 Stream ended")
                            break
                        elif msg_type == "error":
                            print(f"❌ Error: {msg_data.get('message', 'Unknown error')}")
                            self.results["errors"].append(msg_data)
                        else:
                            print(f"📨 {msg_type}: {str(msg_data)[:100]}...")
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 5 == 0:  # Print every 5 seconds
                            print(f"⏳ Waiting... ({timeout_count}s)")
                        continue
                
                self.results["streaming_messages"] = messages
                return len(messages) > 0
                
        except Exception as e:
            print(f"❌ Research streaming test failed: {e}")
            self.results["errors"].append({"test": "research_streaming", "error": str(e)})
            return False
    
    async def test_analyze_streaming(self):
        """Test analyze endpoint with streaming."""
        print("📊 Testing analyze streaming...")
        
        endpoint_url = f"{self.server_url}/ws/analyze/test_user?company=Tesla"
        
        try:
            async with websockets.connect(endpoint_url) as websocket:
                request = {
                    "message": "Analyze Tesla's financial performance and provide key insights",
                    "streaming": True
                }
                
                print(f"📤 Sending request: {request['message']}")
                await websocket.send(json.dumps(request))
                
                # Collect messages for shorter time (analyze is faster)
                messages = []
                timeout_count = 0
                max_timeout = 30
                
                while timeout_count < max_timeout:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        msg_data = json.loads(message)
                        messages.append(msg_data)
                        
                        if msg_data.get("type") == "stream_end":
                            break
                        elif msg_data.get("type") == "mcp_tool_stream":
                            tool_data = msg_data.get("data", {})
                            print(f"🔧 MCP Tool: {tool_data.get('type')} - {tool_data.get('tool_name')}")
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        continue
                
                return len(messages) > 0
                
        except Exception as e:
            print(f"❌ Analyze streaming test failed: {e}")
            self.results["errors"].append({"test": "analyze_streaming", "error": str(e)})
            return False
    
    async def run_validation_tests(self):
        """Run all validation tests."""
        print("🚀 Starting MCP Streaming Validation Tests")
        print("=" * 60)
        
        # Check server health
        if not await self.check_server_health():
            print("❌ Server not available")
            return False
        
        print("✅ Server is running")
        
        # Test research streaming
        self.results["tests_run"] += 1
        if await self.test_research_streaming():
            self.results["tests_passed"] += 1
            print("✅ Research streaming test passed")
        else:
            print("❌ Research streaming test failed")
        
        print("-" * 40)
        
        # Test analyze streaming
        self.results["tests_run"] += 1
        if await self.test_analyze_streaming():
            self.results["tests_passed"] += 1
            print("✅ Analyze streaming test passed")
        else:
            print("❌ Analyze streaming test failed")
        
        # Print results summary
        print("\n" + "=" * 60)
        print("📊 VALIDATION RESULTS")
        print("=" * 60)
        
        print(f"Tests run: {self.results['tests_run']}")
        print(f"Tests passed: {self.results['tests_passed']}")
        print(f"Success rate: {(self.results['tests_passed']/self.results['tests_run']*100):.1f}%")
        
        print(f"\nStreaming messages received: {len(self.results['streaming_messages'])}")
        print(f"MCP tool messages received: {len(self.results['mcp_tool_messages'])}")
        print(f"Errors encountered: {len(self.results['errors'])}")
        
        # Analyze message types
        if self.results['streaming_messages']:
            message_types = {}
            for msg in self.results['streaming_messages']:
                msg_type = msg.get('type', 'unknown')
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
            
            print(f"\nMessage type breakdown:")
            for msg_type, count in message_types.items():
                print(f"  {msg_type}: {count}")
        
        # Check for MCP streaming
        has_mcp_streaming = len(self.results['mcp_tool_messages']) > 0
        print(f"\n🔧 MCP Tool Streaming: {'✅ DETECTED' if has_mcp_streaming else '⚠️ NOT DETECTED'}")
        
        if has_mcp_streaming:
            print("🎉 SUCCESS: MCP tool streaming is working!")
        else:
            print("⚠️ MCP tool streaming not detected - implementation may be needed")
        
        # Save results
        results_file = f"mcp_streaming_validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n💾 Results saved to: {results_file}")
        
        return self.results['tests_passed'] == self.results['tests_run']


async def main():
    """Run the validation tests."""
    validator = MCPStreamingValidator()
    success = await validator.run_validation_tests()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️ Some tests failed - check results for details")


if __name__ == "__main__":
    asyncio.run(main())
