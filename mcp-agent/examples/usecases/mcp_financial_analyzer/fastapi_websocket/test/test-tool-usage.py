#!/usr/bin/env python3
"""
Test to verify that MCP tools are actually being called during research.
"""

import asyncio
import json
import websockets
import requests


async def test_tool_usage():
    """Test that research actually uses MCP tools."""
    
    # Check server health
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"✅ Server health: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not available: {e}")
        return False
    
    # Test WebSocket connection with a request that should trigger tool usage
    endpoint_url = "ws://localhost:8000/ws/research/test_user?company=Apple Inc."
    
    print(f"🔗 Connecting to: {endpoint_url}")
    
    try:
        async with websockets.connect(endpoint_url) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Send a request that should definitely trigger g-search-mcp
            request = {
                "message": "Search for Apple Inc. stock price today and latest quarterly earnings. I need current, real-time information.",
                "streaming": True
            }
            
            print(f"📤 Sending: {request['message']}")
            await websocket.send(json.dumps(request))
            
            # Collect messages and look for tool usage indicators
            messages = []
            tool_calls_detected = []
            timeout_count = 0
            max_timeout = 60  # 60 second timeout for research with tools
            
            while timeout_count < max_timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    msg_data = json.loads(message)
                    messages.append(msg_data)
                    
                    msg_type = msg_data.get("type", "unknown")
                    
                    if msg_type == "system":
                        print(f"🔔 System: {msg_data.get('message', '')}")
                    elif msg_type == "stream_chunk":
                        content = msg_data.get("message", "")
                        if content.strip():
                            # Look for indicators of tool usage in the content
                            if any(keyword in content.lower() for keyword in [
                                "searching", "search", "found", "according to", 
                                "based on", "from", "source", "website", "url"
                            ]):
                                print(f"🔍 Tool usage indicator: {content[:100]}...")
                            else:
                                print(f"📦 Chunk: {content[:80]}{'...' if len(content) > 80 else ''}")
                    elif msg_type == "mcp_tool_stream":
                        tool_data = msg_data.get("data", {})
                        tool_type = tool_data.get("type", "unknown")
                        tool_name = tool_data.get("tool_name", "unknown")
                        print(f"🔧 MCP Tool {tool_type}: {tool_name}")
                        tool_calls_detected.append(tool_data)
                    elif msg_type == "stream_end":
                        print(f"🏁 Stream ended: {msg_data.get('message', '')}")
                        break
                    elif msg_type == "error":
                        print(f"❌ Error: {msg_data.get('message', '')}")
                        break
                    else:
                        print(f"📨 {msg_type}: {str(msg_data)[:100]}...")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 10 == 0:  # Print every 10 seconds
                        print(f"⏳ Waiting for tool usage... ({timeout_count}s)")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("🔌 WebSocket connection closed")
                    break
                except Exception as e:
                    print(f"❌ Error receiving message: {e}")
                    break
            
            print(f"\n📊 Analysis Results:")
            print(f"   Total messages: {len(messages)}")
            print(f"   MCP tool calls detected: {len(tool_calls_detected)}")
            
            if messages:
                message_types = {}
                for msg in messages:
                    msg_type = msg.get('type', 'unknown')
                    message_types[msg_type] = message_types.get(msg_type, 0) + 1
                
                print(f"   Message types:")
                for msg_type, count in message_types.items():
                    print(f"     {msg_type}: {count}")
                
                # Analyze content for tool usage indicators
                all_content = ""
                for msg in messages:
                    if msg.get("type") == "stream_chunk":
                        all_content += msg.get("message", "")
                
                # Look for indicators that tools were actually used
                tool_indicators = [
                    "search", "found", "according to", "based on", "source",
                    "website", "url", "from yahoo", "from google", "latest",
                    "current", "today", "recent"
                ]
                
                indicators_found = []
                for indicator in tool_indicators:
                    if indicator in all_content.lower():
                        indicators_found.append(indicator)
                
                print(f"\n🔍 Tool usage indicators found: {len(indicators_found)}")
                if indicators_found:
                    print(f"   Indicators: {', '.join(indicators_found[:5])}{'...' if len(indicators_found) > 5 else ''}")
                
                # Check if response seems to contain real-time data
                has_realtime_data = any(keyword in all_content.lower() for keyword in [
                    "$", "price", "earnings", "revenue", "stock", "market", "trading"
                ])
                
                print(f"🔢 Contains financial data: {has_realtime_data}")
                
                # Final assessment
                if len(tool_calls_detected) > 0:
                    print("\n🎉 SUCCESS: MCP tool streaming detected!")
                    return True
                elif len(indicators_found) > 2 and has_realtime_data:
                    print("\n⚠️ PARTIAL: Tool usage likely but streaming not detected")
                    print("   (Tools may be working but streaming integration needs work)")
                    return True
                else:
                    print("\n❌ FAILED: No clear evidence of tool usage")
                    print("   Response appears to be generated without real data")
                    return False
            else:
                print("❌ No messages received")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False


async def main():
    """Run the tool usage test."""
    print("🚀 Testing MCP Tool Usage")
    print("=" * 50)
    
    success = await test_tool_usage()
    
    if success:
        print("\n🎉 Tool usage test passed!")
    else:
        print("\n❌ Tool usage test failed!")
        print("\nThis suggests that:")
        print("1. MCP tools may not be properly configured")
        print("2. Research agent may not be calling tools")
        print("3. Tool streaming integration needs work")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
