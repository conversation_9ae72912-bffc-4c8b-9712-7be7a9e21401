#!/usr/bin/env python3
"""
Comprehensive test suite for VLLM streaming functionality in the MCP Financial Analyzer WebSocket interface.
This follows Test-Driven Development (TDD) approach to ensure VLLM streaming works correctly.
"""

import asyncio
import json
import pytest
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Dict, Any, AsyncGenerator
import websockets
from urllib.parse import quote

# Import the classes we'll be testing and implementing
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from session_manager import (
    FinancialSession,
    FinancialSessionManager,
    SessionType,
    StreamingOpenAILLM,  # Current implementation
    StreamingVLLMLLM     # New VLLM implementation
)


class TestVLLMStreamingCore:
    """Test core VLLM streaming functionality."""
    
    @pytest.fixture
    def mock_vllm_client(self):
        """Mock VLLM client for testing."""
        client = Mock()
        client.chat = Mock()
        client.chat.completions = Mock()
        return client
    
    @pytest.fixture
    def mock_vllm_llm(self):
        """Mock VLLMAugmentedLLM for testing."""
        from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
        llm = Mock(spec=VLLMAugmentedLLM)
        llm.generate_str = AsyncMock()
        return llm
    
    @pytest.mark.asyncio
    async def test_streaming_vllm_llm_instantiation(self, mock_vllm_llm):
        """Test that StreamingVLLMLLM can be instantiated with VLLMAugmentedLLM."""
        # This test will fail initially - we need to implement StreamingVLLMLLM
        streaming_llm = StreamingVLLMLLM(mock_vllm_llm)
        
        assert streaming_llm is not None
        assert hasattr(streaming_llm, 'llm')
        assert streaming_llm.llm == mock_vllm_llm
    
    @pytest.mark.asyncio
    async def test_streaming_vllm_llm_has_streaming_method(self, mock_vllm_llm):
        """Test that StreamingVLLMLLM has generate_str_streaming method."""
        streaming_llm = StreamingVLLMLLM(mock_vllm_llm)
        
        assert hasattr(streaming_llm, 'generate_str_streaming')
        assert callable(streaming_llm.generate_str_streaming)
    
    @pytest.mark.asyncio
    async def test_vllm_streaming_with_callback(self, mock_vllm_llm):
        """Test VLLM streaming with callback function."""
        # Set up mock LLM to return expected response
        mock_vllm_llm.generate_str = AsyncMock(return_value="Hello world!")
        mock_vllm_llm.instruction = "Test instruction"

        streaming_llm = StreamingVLLMLLM(mock_vllm_llm)

        # Collect streamed chunks
        streamed_chunks = []

        async def stream_callback(chunk: str):
            streamed_chunks.append(chunk)

        # Test streaming (will fallback to non-streaming due to no VLLM server)
        result = await streaming_llm.generate_str_streaming(
            message="Test message",
            stream_callback=stream_callback
        )

        # Verify chunks were streamed (including initialization and fallback messages)
        assert len(streamed_chunks) > 0

        # Check that we got some content (either from streaming or fallback)
        full_content = "".join(streamed_chunks)
        assert "Initializing VLLM analysis" in full_content or "Hello world" in full_content

        # Result should be the fallback response
        assert result == "Hello world!"
    
    @pytest.mark.asyncio
    async def test_vllm_streaming_performance(self, mock_vllm_llm):
        """Test that VLLM streaming has acceptable performance characteristics."""
        # Set up mock for fallback behavior
        mock_response = " ".join([f"chunk_{i}" for i in range(20)])
        mock_vllm_llm.generate_str = AsyncMock(return_value=mock_response)
        mock_vllm_llm.instruction = "Performance test instruction"

        streaming_llm = StreamingVLLMLLM(mock_vllm_llm)

        # Track timing
        chunk_times = []
        start_time = time.time()

        async def timed_callback(chunk: str):
            chunk_times.append(time.time() - start_time)

        # Test streaming performance (will use fallback)
        result = await streaming_llm.generate_str_streaming(
            message="Performance test",
            stream_callback=timed_callback
        )

        # Verify we received chunks (at least initialization + fallback messages + chunked response)
        assert len(chunk_times) >= 3

        # Verify we got the expected result
        assert result == mock_response

        # Check that first chunk arrives quickly (initialization message)
        assert chunk_times[0] < 0.1  # First chunk within 100ms
    
    @pytest.mark.asyncio
    async def test_vllm_streaming_error_handling(self, mock_vllm_llm):
        """Test error handling in VLLM streaming."""
        # Mock an error during streaming
        async def mock_stream_with_error():
            yield {"choices": [{"delta": {"content": "Start"}}]}
            raise Exception("VLLM connection error")
        
        mock_vllm_llm.client = Mock()
        mock_vllm_llm.client.chat = Mock()
        mock_vllm_llm.client.chat.completions = Mock()
        mock_vllm_llm.client.chat.completions.create = Mock(return_value=mock_stream_with_error())
        
        streaming_llm = StreamingVLLMLLM(mock_vllm_llm)
        
        error_received = False
        chunks_received = []
        
        async def error_callback(chunk: str):
            nonlocal error_received
            if "error" in chunk.lower() or "failed" in chunk.lower():
                error_received = True
            chunks_received.append(chunk)
        
        # Should handle error gracefully and provide fallback
        result = await streaming_llm.generate_str_streaming(
            message="Error test",
            stream_callback=error_callback
        )
        
        # Should have received some content before error
        assert len(chunks_received) > 0
        # Should have handled error gracefully (either through callback or fallback)
        assert error_received or result is not None


class TestVLLMSessionIntegration:
    """Test VLLM integration with FinancialSession."""
    
    @pytest.fixture
    def mock_vllm_augmented_llm(self):
        """Mock VLLMAugmentedLLM for session testing."""
        from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
        llm = Mock(spec=VLLMAugmentedLLM)
        llm.generate_str = AsyncMock(return_value="Mock VLLM response")
        return llm
    
    @pytest.mark.asyncio
    async def test_financial_session_uses_vllm_streaming(self, mock_vllm_augmented_llm):
        """Test that FinancialSession can use VLLM streaming instead of OpenAI."""
        # Create a session that should use VLLM
        session = FinancialSession("test_user", "test_session", SessionType.RESEARCH)

        # Mock the agent creation and LLM attachment
        with patch('session_manager.create_research_agent') as mock_create_agent:
            mock_agent = Mock()
            mock_agent.__aenter__ = AsyncMock(return_value=mock_agent)
            mock_agent.__aexit__ = AsyncMock(return_value=None)
            mock_agent.attach_llm = AsyncMock(return_value=mock_vllm_augmented_llm)
            mock_create_agent.return_value = mock_agent

            # Initialize with VLLM
            await session.initialize("Test Company")

            # Verify VLLM is used instead of OpenAI
            assert session.streaming_llm is not None
            assert isinstance(session.streaming_llm, StreamingVLLMLLM)
            assert session.llm == mock_vllm_augmented_llm
    
    @pytest.mark.asyncio
    async def test_all_session_types_support_vllm_streaming(self):
        """Test that all session types (research, analyze, report, full_analysis) support VLLM streaming."""
        session_types = [
            SessionType.RESEARCH,
            SessionType.ANALYZE, 
            SessionType.REPORT,
            SessionType.FULL_ANALYSIS
        ]
        
        for session_type in session_types:
            session = FinancialSession("test_user", f"test_session_{session_type.value}", session_type)
            
            # Mock initialization
            with patch.object(session, '_initialize_agents'):
                with patch('session_manager.VLLMAugmentedLLM') as mock_vllm:
                    mock_llm = Mock()
                    mock_vllm.return_value = mock_llm
                    
                    await session.initialize("Test Company")
                    
                    # Each session type should have streaming capability
                    assert hasattr(session, 'streaming_llm')
                    # The streaming_llm should be StreamingVLLMLLM, not StreamingOpenAILLM
                    if session.streaming_llm:
                        assert isinstance(session.streaming_llm, StreamingVLLMLLM)


class TestVLLMWebSocketEndpoints:
    """Test VLLM streaming through WebSocket endpoints."""
    
    @pytest.fixture
    def mock_websocket_server(self):
        """Mock WebSocket server for testing."""
        return Mock()
    
    @pytest.mark.asyncio
    async def test_research_endpoint_vllm_streaming(self):
        """Test research endpoint with VLLM streaming."""
        # This will test the actual WebSocket endpoint
        # We'll mock the underlying VLLM calls
        
        with patch('session_manager.VLLMAugmentedLLM') as mock_vllm_class:
            mock_llm = Mock()
            mock_vllm_class.return_value = mock_llm
            
            # Mock streaming response
            async def mock_streaming_response(message, stream_callback):
                chunks = ["Analyzing ", "Test Company ", "financial data..."]
                for chunk in chunks:
                    await stream_callback(chunk)
                    await asyncio.sleep(0.01)  # Small delay
                return "".join(chunks)
            
            # Create StreamingVLLMLLM mock
            with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
                mock_streaming = Mock()
                mock_streaming.generate_str_streaming = mock_streaming_response
                mock_streaming_class.return_value = mock_streaming
                
                # Test session manager
                session_manager = FinancialSessionManager()
                await session_manager.initialize()
                
                # Get session
                session = await session_manager.get_or_create_session(
                    "test_user", SessionType.RESEARCH, "Test Company"
                )
                
                # Test streaming message processing
                chunks_received = []
                
                async def collect_chunks(chunk):
                    chunks_received.append(chunk)
                
                result = await session.process_message_streaming(
                    "Analyze Test Company", collect_chunks
                )
                
                # Verify streaming worked
                assert len(chunks_received) > 0
                assert any("Analyzing" in chunk for chunk in chunks_received)
                assert result is not None
    
    @pytest.mark.asyncio
    async def test_all_endpoints_vllm_streaming_compatibility(self):
        """Test that all endpoints work with VLLM streaming."""
        endpoints = ["research", "analyze", "report", "full_analysis"]
        session_types = [SessionType.RESEARCH, SessionType.ANALYZE, SessionType.REPORT, SessionType.FULL_ANALYSIS]
        
        for endpoint, session_type in zip(endpoints, session_types):
            with patch('session_manager.VLLMAugmentedLLM') as mock_vllm_class:
                mock_llm = Mock()
                mock_vllm_class.return_value = mock_llm
                
                with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
                    mock_streaming = Mock()
                    mock_streaming.generate_str_streaming = AsyncMock(return_value=f"Mock {endpoint} response")
                    mock_streaming_class.return_value = mock_streaming
                    
                    # Test each endpoint
                    session_manager = FinancialSessionManager()
                    await session_manager.initialize()
                    
                    session = await session_manager.get_or_create_session(
                        f"test_user_{endpoint}", session_type, "Test Company"
                    )
                    
                    # Verify session has VLLM streaming capability
                    assert session.streaming_llm is not None
                    # Note: In test, this will be a Mock, but in real usage it would be StreamingVLLMLLM
                    assert hasattr(session.streaming_llm, 'generate_str_streaming')


class TestVLLMStreamingConfiguration:
    """Test VLLM streaming configuration and settings."""
    
    @pytest.mark.asyncio
    async def test_vllm_api_configuration(self):
        """Test VLLM API endpoint configuration."""
        # Test default configuration with proper mock
        mock_llm = Mock()
        mock_llm.vllm_api_base = "http://test:8000/v1"
        mock_llm.vllm_api_key = "test-key"

        streaming_llm = StreamingVLLMLLM(mock_llm)

        # Should have configurable API endpoint
        assert hasattr(streaming_llm, 'api_base')
        assert streaming_llm.api_base == "http://test:8000/v1"

        # Test custom configuration
        streaming_llm_custom = StreamingVLLMLLM(
            mock_llm,
            api_base='http://custom-vllm:8000/v1',
            api_key='custom-key'
        )
        assert streaming_llm_custom.api_base == 'http://custom-vllm:8000/v1'
        assert streaming_llm_custom.api_key == 'custom-key'
    
    @pytest.mark.asyncio
    async def test_vllm_model_selection(self):
        """Test VLLM model selection and configuration."""
        mock_llm = Mock()
        mock_llm.vllm_api_base = "http://test:8000/v1"
        mock_llm.vllm_api_key = "test-key"
        mock_llm.generate_str = AsyncMock(return_value="Test response")
        mock_llm.instruction = "Test instruction"

        streaming_llm = StreamingVLLMLLM(mock_llm)

        # Should support model configuration
        test_models = [
            "Qwen/Qwen3-8B",
            "meta-llama/Llama-3.1-8B-Instruct",
            "microsoft/DialoGPT-medium"
        ]

        for model in test_models:
            # Should be able to configure different models (will fallback to mock)
            result = await streaming_llm.generate_str_streaming(
                message="Test",
                model=model,
                stream_callback=AsyncMock()
            )
            # Should get the mock response
            assert result == "Test response"


if __name__ == "__main__":
    print("🧪 Running VLLM Streaming Tests")
    print("=" * 60)
    
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
