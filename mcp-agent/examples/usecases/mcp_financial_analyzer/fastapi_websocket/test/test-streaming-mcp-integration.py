#!/usr/bin/env python3
"""
Comprehensive TDD test suite for real-time streaming of MCP service interactions.

This test suite validates streaming functionality for:
1. g-search-mcp service queries and results
2. mcp-server-fetch service URL requests and responses  
3. WebSocket message flow and error handling
4. Integration with existing streaming infrastructure

Following TDD principles: Write tests first, then implement functionality.
"""

import asyncio
import json
import pytest
import websockets
from unittest.mock import Mock, AsyncMock, patch
from typing import List, Dict, Any, Callable
import time
from datetime import datetime

# Import the components we'll be testing/implementing
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from session_manager import (
    FinancialSession,
    FinancialSessionManager,
    SessionType,
    StreamingVLLMLLM,
    MCPToolStreamingWrapper
)


class TestMCPToolStreaming:
    """Test suite for MCP tool streaming functionality."""
    
    @pytest.fixture
    def mock_stream_callback(self):
        """Mock stream callback for testing."""
        callback = AsyncMock()
        return callback
    
    @pytest.fixture
    def mock_mcp_agent(self):
        """Mock MCP agent with tool calling capabilities."""
        agent = Mock()
        agent.call_tool = AsyncMock()
        return agent
    
    @pytest.mark.asyncio
    async def test_g_search_streaming_basic(self, mock_stream_callback, mock_mcp_agent):
        """Test basic g-search-mcp streaming functionality."""
        # Mock g-search tool response
        mock_search_result = {
            "results": [
                {
                    "title": "Apple Inc. Stock Price Today",
                    "url": "https://finance.yahoo.com/quote/AAPL",
                    "snippet": "Apple Inc. (AAPL) stock price is $150.25, up 2.5% today"
                }
            ]
        }
        mock_mcp_agent.call_tool.return_value = mock_search_result
        
        # Use the imported component
        
        # Create streaming wrapper
        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)
        
        # Test streaming tool call
        result = await streaming_wrapper.call_tool_streaming(
            "g-search", 
            {"query": "Apple Inc. stock price today"}
        )
        
        # Verify streaming messages were sent
        assert mock_stream_callback.call_count >= 2
        
        # Check tool_call_start message
        start_call = mock_stream_callback.call_args_list[0][0][0]
        assert start_call["type"] == "tool_call_start"
        assert start_call["tool_name"] == "g-search"
        assert start_call["args"]["query"] == "Apple Inc. stock price today"
        
        # Check tool_call_result message
        result_call = mock_stream_callback.call_args_list[-1][0][0]
        assert result_call["type"] == "tool_call_result"
        assert result_call["tool_name"] == "g-search"
        assert result_call["result"] == mock_search_result
        
        # Verify actual tool was called
        mock_mcp_agent.call_tool.assert_called_once_with("g-search", {"query": "Apple Inc. stock price today"})
        assert result == mock_search_result
    
    @pytest.mark.asyncio
    async def test_fetch_streaming_basic(self, mock_stream_callback, mock_mcp_agent):
        """Test basic mcp-server-fetch streaming functionality."""
        # Mock fetch tool response
        mock_fetch_result = {
            "url": "https://finance.yahoo.com/quote/AAPL",
            "content": "Apple Inc. (AAPL) - Current Price: $150.25",
            "status_code": 200
        }
        mock_mcp_agent.call_tool.return_value = mock_fetch_result
        
        # Create streaming wrapper
        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)
        
        # Test streaming fetch call
        result = await streaming_wrapper.call_tool_streaming(
            "fetch", 
            {"url": "https://finance.yahoo.com/quote/AAPL"}
        )
        
        # Verify streaming messages
        assert mock_stream_callback.call_count >= 2
        
        # Check tool_call_start message
        start_call = mock_stream_callback.call_args_list[0][0][0]
        assert start_call["type"] == "tool_call_start"
        assert start_call["tool_name"] == "fetch"
        assert start_call["args"]["url"] == "https://finance.yahoo.com/quote/AAPL"
        
        # Check tool_call_result message
        result_call = mock_stream_callback.call_args_list[-1][0][0]
        assert result_call["type"] == "tool_call_result"
        assert result_call["tool_name"] == "fetch"
        assert result_call["result"] == mock_fetch_result
        
        # Verify tool execution
        mock_mcp_agent.call_tool.assert_called_once_with("fetch", {"url": "https://finance.yahoo.com/quote/AAPL"})
        assert result == mock_fetch_result
    
    @pytest.mark.asyncio
    async def test_tool_streaming_error_handling(self, mock_stream_callback, mock_mcp_agent):
        """Test error handling in tool streaming."""
        # Mock tool call failure
        mock_mcp_agent.call_tool.side_effect = Exception("Network error")
        
        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)
        
        # Test error handling
        with pytest.raises(Exception, match="Network error"):
            await streaming_wrapper.call_tool_streaming("g-search", {"query": "test"})
        
        # Verify error was streamed
        assert mock_stream_callback.call_count >= 2
        
        # Check tool_call_start was sent
        start_call = mock_stream_callback.call_args_list[0][0][0]
        assert start_call["type"] == "tool_call_start"
        
        # Check tool_call_error was sent
        error_call = mock_stream_callback.call_args_list[-1][0][0]
        assert error_call["type"] == "tool_call_error"
        assert "Network error" in error_call["error"]
    
    @pytest.mark.asyncio
    async def test_concurrent_tool_streaming(self, mock_stream_callback, mock_mcp_agent):
        """Test concurrent tool calls with proper streaming."""
        # Mock different responses for different tools
        def mock_tool_call(tool_name, args):
            if tool_name == "g-search":
                return {"results": [{"title": "Search Result"}]}
            elif tool_name == "fetch":
                return {"content": "Fetched Content", "status_code": 200}
            return {}
        
        mock_mcp_agent.call_tool.side_effect = mock_tool_call
        
        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)
        
        # Execute concurrent tool calls
        tasks = [
            streaming_wrapper.call_tool_streaming("g-search", {"query": "Apple"}),
            streaming_wrapper.call_tool_streaming("fetch", {"url": "https://example.com"})
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Verify both tools were called
        assert len(results) == 2
        assert results[0]["results"][0]["title"] == "Search Result"
        assert results[1]["content"] == "Fetched Content"
        
        # Verify streaming messages for both tools
        assert mock_stream_callback.call_count >= 4  # At least 2 messages per tool


class TestFinancialSessionMCPStreaming:
    """Test MCP streaming integration with FinancialSession."""
    
    @pytest.mark.asyncio
    async def test_research_session_with_mcp_streaming(self):
        """Test research session with MCP tool streaming."""
        # Mock the session initialization
        with patch('session_manager.create_research_agent') as mock_create_agent:
            with patch('session_manager.VLLMAugmentedLLM') as mock_vllm:
                # Setup mocks
                mock_agent = Mock()
                mock_agent.__aenter__ = AsyncMock(return_value=mock_agent)
                mock_agent.__aexit__ = AsyncMock(return_value=None)
                
                mock_llm = Mock()
                mock_agent.attach_llm = AsyncMock(return_value=mock_llm)
                mock_create_agent.return_value = mock_agent
                
                # Create session
                session = FinancialSession("test_user", "test_session", SessionType.RESEARCH)
                await session.initialize("Test Company")
                
                # Verify MCP streaming wrapper is created
                assert hasattr(session, 'mcp_streaming_wrapper')
                assert session.mcp_streaming_wrapper is not None
    
    @pytest.mark.asyncio
    async def test_session_streaming_with_tool_calls(self):
        """Test session streaming that includes MCP tool calls."""
        collected_messages = []
        
        async def collect_stream_messages(message):
            if isinstance(message, dict):
                collected_messages.append(message)
            else:
                # Handle string messages (existing LLM streaming)
                collected_messages.append({"type": "llm_chunk", "content": message})
        
        # Mock session with MCP streaming
        with patch('session_manager.create_research_agent') as mock_create_agent:
            with patch('session_manager.VLLMAugmentedLLM') as mock_vllm:
                with patch('session_manager.StreamingVLLMLLM') as mock_streaming_llm:
                    # Setup comprehensive mocks
                    mock_agent = Mock()
                    mock_agent.__aenter__ = AsyncMock(return_value=mock_agent)
                    mock_agent.__aexit__ = AsyncMock(return_value=None)
                    mock_agent.call_tool = AsyncMock(return_value={"results": ["test result"]})
                    
                    mock_llm = Mock()
                    mock_agent.attach_llm = AsyncMock(return_value=mock_llm)
                    mock_create_agent.return_value = mock_agent
                    
                    # Mock streaming LLM to simulate tool calls
                    mock_streaming_instance = Mock()
                    mock_streaming_instance.generate_str_streaming = AsyncMock(
                        return_value="Analysis complete with tool results"
                    )
                    mock_streaming_llm.return_value = mock_streaming_instance
                    
                    # Create and initialize session
                    session = FinancialSession("test_user", "test_session", SessionType.RESEARCH)
                    await session.initialize("Test Company")
                    
                    # Process message with streaming
                    result = await session.process_message_streaming(
                        "Research Apple Inc. stock", 
                        collect_stream_messages
                    )
                    
                    # Verify we got streaming messages
                    assert len(collected_messages) > 0
                    
                    # Should include both LLM streaming and tool streaming messages
                    message_types = [msg.get("type") for msg in collected_messages if isinstance(msg, dict)]
                    
                    # At minimum, we should have LLM chunks
                    # In full implementation, we'd also have tool_call_start, tool_call_result
                    assert any("llm" in str(msg_type) for msg_type in message_types)


class TestWebSocketMCPStreaming:
    """Test WebSocket integration with MCP streaming."""
    
    @pytest.mark.asyncio
    async def test_websocket_mcp_streaming_messages(self):
        """Test WebSocket receives MCP tool streaming messages."""
        # This test would require a running FastAPI server
        # For now, we'll test the message formatting
        
        # Mock WebSocket message handler
        websocket_messages = []
        
        async def mock_websocket_send(message):
            websocket_messages.append(json.loads(message))
        
        # Simulate tool streaming messages
        tool_messages = [
            {
                "type": "tool_call_start",
                "tool_name": "g-search", 
                "args": {"query": "Apple stock"},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "tool_call_result",
                "tool_name": "g-search",
                "result": {"results": [{"title": "Apple Stock Info"}]},
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        # Test message formatting for WebSocket
        for tool_msg in tool_messages:
            websocket_msg = {
                "type": "mcp_tool_stream",
                "data": tool_msg,
                "user_id": "test_user"
            }
            await mock_websocket_send(json.dumps(websocket_msg))
        
        # Verify messages were properly formatted
        assert len(websocket_messages) == 2
        assert websocket_messages[0]["type"] == "mcp_tool_stream"
        assert websocket_messages[0]["data"]["tool_name"] == "g-search"
        assert websocket_messages[1]["data"]["type"] == "tool_call_result"
    
    @pytest.mark.asyncio
    async def test_websocket_integration_full_flow(self):
        """Test full WebSocket flow with MCP streaming (integration test)."""
        # This would be a full integration test requiring:
        # 1. Running FastAPI server
        # 2. Real WebSocket connection
        # 3. MCP services available
        
        # For TDD, we define the expected behavior:
        expected_message_flow = [
            "stream_chunk",      # LLM starts processing
            "mcp_tool_stream",   # Tool call begins
            "mcp_tool_stream",   # Tool result received
            "stream_chunk",      # LLM continues with tool result
            "stream_end"         # Analysis complete
        ]
        
        # This test will be implemented after the core components are built
        # It will validate the complete end-to-end streaming flow
        assert len(expected_message_flow) == 5  # Placeholder assertion


class TestMCPStreamingIntegration:
    """Integration tests for MCP streaming with real server."""

    @pytest.mark.asyncio
    async def test_real_websocket_mcp_streaming(self):
        """Test real WebSocket connection with MCP streaming (requires running server)."""
        # This test requires the FastAPI server to be running
        # It will connect to ws://localhost:8000/ws/research/test_user

        server_url = "ws://localhost:8000/ws/research/test_user?company=Apple Inc."

        try:
            # Connect to WebSocket server
            async with websockets.connect(server_url) as websocket:
                # Send research request
                request = {
                    "message": "Research Apple Inc. current stock price and recent news",
                    "streaming": True
                }
                await websocket.send(json.dumps(request))

                # Collect streaming messages
                messages = []
                timeout_count = 0
                max_timeout = 30  # 30 second timeout

                while timeout_count < max_timeout:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        msg_data = json.loads(message)
                        messages.append(msg_data)

                        # Check for stream end
                        if msg_data.get("type") == "stream_end":
                            break

                        # Look for MCP tool streaming messages
                        if msg_data.get("type") == "mcp_tool_stream":
                            print(f"MCP Tool Stream: {msg_data}")

                    except asyncio.TimeoutError:
                        timeout_count += 1
                        continue

                # Verify we received messages
                assert len(messages) > 0, "Should receive streaming messages"

                # Check for expected message types
                message_types = [msg.get("type") for msg in messages]
                assert "stream_chunk" in message_types, "Should have LLM streaming"

                # In full implementation, should also have:
                # assert "mcp_tool_stream" in message_types, "Should have MCP tool streaming"

        except Exception as e:
            pytest.skip(f"Server not running or connection failed: {e}")

    @pytest.mark.asyncio
    async def test_mcp_service_availability(self):
        """Test that required MCP services are available."""
        # This test checks if g-search-mcp and mcp-server-fetch are properly configured

        from mcp_agent.app import MCPApp

        try:
            # Create MCP app to test service availability
            app = MCPApp(name="test_mcp_services")

            async with app.run() as test_app:
                context = test_app.context

                # Check if required servers are configured
                required_servers = ["g-search", "fetch"]
                configured_servers = list(context.config.mcp.servers.keys())

                for server in required_servers:
                    assert server in configured_servers, f"MCP server '{server}' not configured"

                # Test basic tool availability (if servers are running)
                # This would be expanded in full implementation

        except Exception as e:
            pytest.skip(f"MCP services not available: {e}")


class TestMCPStreamingPerformance:
    """Performance tests for MCP streaming functionality."""

    @pytest.mark.asyncio
    async def test_streaming_latency(self, mock_stream_callback, mock_mcp_agent):
        """Test that MCP streaming doesn't add significant latency."""
        # Mock fast tool response
        mock_mcp_agent.call_tool.return_value = {"result": "fast response"}

        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)

        # Measure execution time
        start_time = time.time()
        result = await streaming_wrapper.call_tool_streaming("test-tool", {"arg": "value"})
        end_time = time.time()

        execution_time = end_time - start_time

        # Should complete quickly (under 100ms for mocked calls)
        assert execution_time < 0.1, f"Streaming wrapper too slow: {execution_time}s"
        assert result == {"result": "fast response"}

    @pytest.mark.asyncio
    async def test_high_frequency_streaming(self, mock_stream_callback, mock_mcp_agent):
        """Test streaming with high frequency tool calls."""
        call_count = 0

        def mock_tool_call(tool_name, args):
            nonlocal call_count
            call_count += 1
            return {"call_number": call_count, "tool": tool_name}

        mock_mcp_agent.call_tool.side_effect = mock_tool_call

        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)

        # Execute multiple rapid tool calls
        num_calls = 10
        tasks = []
        for i in range(num_calls):
            task = streaming_wrapper.call_tool_streaming(f"tool-{i}", {"index": i})
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # Verify all calls completed
        assert len(results) == num_calls
        assert call_count == num_calls

        # Verify streaming messages for all calls
        # Should have at least 2 messages per call (start + result)
        assert mock_stream_callback.call_count >= num_calls * 2


class TestMCPStreamingErrorRecovery:
    """Test error recovery and resilience in MCP streaming."""

    @pytest.mark.asyncio
    async def test_partial_tool_failure_recovery(self, mock_stream_callback, mock_mcp_agent):
        """Test recovery when some tools fail but others succeed."""
        call_count = 0

        def mock_tool_call(tool_name, args):
            nonlocal call_count
            call_count += 1
            if call_count == 2:  # Second call fails
                raise Exception("Simulated tool failure")
            return {"success": True, "call": call_count}

        mock_mcp_agent.call_tool.side_effect = mock_tool_call

        streaming_wrapper = MCPToolStreamingWrapper(mock_mcp_agent, mock_stream_callback)

        # Execute multiple tool calls, expecting one to fail
        results = []
        errors = []

        for i in range(3):
            try:
                result = await streaming_wrapper.call_tool_streaming(f"tool-{i}", {"index": i})
                results.append(result)
            except Exception as e:
                errors.append(str(e))

        # Should have 2 successes and 1 failure
        assert len(results) == 2
        assert len(errors) == 1
        assert "Simulated tool failure" in errors[0]

        # Verify streaming messages include error information
        error_messages = [
            call for call in mock_stream_callback.call_args_list
            if len(call[0]) > 0 and isinstance(call[0][0], dict) and call[0][0].get("type") == "tool_call_error"
        ]
        assert len(error_messages) >= 1


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
